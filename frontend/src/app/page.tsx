'use client';

import { useState } from 'react';
import AnimationPreview from '@/components/animation-preview';
import MCPEnhancedPreview from '@/components/mcp-enhanced-preview';
import MCPControlPanel from '@/components/mcp-control-panel';

export default function Home() {
  const [mcpEnabled, setMcpEnabled] = useState(true);
  const [mcpStatus, setMcpStatus] = useState<any>(null);

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Motion Agent - MCP Enhanced Animation Preview
          </h1>
          <p className="text-gray-600">
            Preview and interact with 3D FBX animations using React Three Fiber with MCP integration
          </p>

          <div className="mt-4 flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={mcpEnabled}
                onChange={(e) => setMcpEnabled(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">Enable MCP Features</span>
            </label>

            {mcpStatus && (
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  mcpStatus.server_running ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm text-gray-600">
                  MCP: {mcpStatus.server_running ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            )}
          </div>
        </header>

        <main className="space-y-8">
          {/* MCP Control Panel */}
          {mcpEnabled && (
            <MCPControlPanel
              apiBaseUrl="http://localhost:8000"
              onStatusChange={setMcpStatus}
            />
          )}

          {/* Animation Preview */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              {mcpEnabled ? 'MCP Enhanced Animation Preview' : 'Standard Animation Preview'}
            </h2>

            {mcpEnabled ? (
              <MCPEnhancedPreview
                fbxUrl="/test_animation.fbx"
                animationName="Samba Dance"
                autoPlay={true}
                showGrid={true}
                showStats={false}
                cameraPosition={[5, 5, 5]}
                backgroundColor="#f8fafc"
                enableMCPFeatures={true}
                apiBaseUrl="http://localhost:8000"
              />
            ) : (
              <AnimationPreview
                fbxUrl="/test_animation.fbx"
                animationName="Samba Dance"
                autoPlay={true}
                showGrid={true}
                showStats={false}
                cameraPosition={[5, 5, 5]}
                backgroundColor="#f8fafc"
              />
            )}
          </div>

          {/* Demo without FBX file to show MCP features */}
          {mcpEnabled && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">
                MCP Live Scene Preview
              </h2>
              <MCPEnhancedPreview
                animationName="Live Blender Scene"
                autoPlay={false}
                showGrid={true}
                showStats={false}
                cameraPosition={[5, 5, 5]}
                backgroundColor="#f0f8ff"
                enableMCPFeatures={true}
                apiBaseUrl="http://localhost:8000"
              />
            </div>
          )}

          {/* Controls and Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                3D Viewer Controls
              </h3>
              <ul className="text-gray-600 space-y-1">
                <li>• Left click + drag: Rotate camera</li>
                <li>• Right click + drag: Pan camera</li>
                <li>• Scroll wheel: Zoom in/out</li>
                <li>• Animation plays automatically</li>
              </ul>
            </div>

            {mcpEnabled && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-800">
                  MCP Features
                </h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• Real-time Blender scene information</li>
                  <li>• Live viewport screenshots</li>
                  <li>• Server status monitoring</li>
                  <li>• Automatic addon management</li>
                  <li>• Enhanced animation controls</li>
                </ul>
              </div>
            )}
          </div>

          {/* API Information */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">
              API Integration
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Standard Endpoints</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• POST /generate-actions - Generate actions from text</li>
                  <li>• POST /export-fbx - Export FBX animation</li>
                  <li>• GET /health - System health check</li>
                  <li>• GET /output-files - List generated files</li>
                </ul>
              </div>

              {mcpEnabled && (
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">MCP Endpoints</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• POST /mcp-server - Manage MCP server</li>
                    <li>• GET /scene-info - Get Blender scene info</li>
                    <li>• GET /scene-screenshot - Capture screenshot</li>
                    <li>• POST /execute-blender-code - Execute code</li>
                    <li>• POST /manage-addon - Manage Blender addon</li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
